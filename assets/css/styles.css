@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* CSS Variables for theming */
:root {
    /* Light theme variables */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-tertiary: #9ca3af;
    --border-primary: #e5e7eb;
    --border-secondary: #d1d5db;
    --card-bg: #ffffff;
    --card-border: #e5e7eb;
    --shadow-medium: rgba(0, 0, 0, 0.1);
    --input-bg: #ffffff;
    --input-placeholder: #9ca3af;
    --scrollbar-track: #f1f5f9;
    --scrollbar-thumb: #cbd5e1;
    --scrollbar-thumb-hover: #94a3b8;
    --code-text: #dc2626;
    --code-bg: #f3f4f6;
    --pre-bg: #1f2937;
    --pre-text: #f9fafb;
}

/* Dark theme colors */
.dark {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #ffffff;
    --text-secondary: #f1f5f9;
    --text-tertiary: #e2e8f0;
    --border-primary: #334155;
    --border-secondary: #475569;
    --shadow-light: rgba(0, 0, 0, 0.3);
    --shadow-medium: rgba(0, 0, 0, 0.4);
    --shadow-heavy: rgba(0, 0, 0, 0.6);
    --card-bg: rgba(30, 41, 59, 0.95);
    --card-border: rgba(51, 65, 85, 0.3);
    --stats-card-bg: rgba(30, 41, 59, 0.8);
    --search-input-bg: rgba(30, 41, 59, 0.95);
    --modal-bg: #1e293b;
    --code-bg: #334155;
    --code-text: #f1f5f9;
    --pre-bg: #0f172a;
    --pre-text: #ffffff;
    --input-bg: #334155;
    --input-placeholder: #cbd5e1;
    --scrollbar-track: #1e293b;
    --scrollbar-thumb: #475569;
    --scrollbar-thumb-hover: #64748b;
}

body {
    font-family: 'Inter', sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.gradient-bg {
    background: linear-gradient(135deg, #1e40af 0%, #3730a3 25%, #581c87 50%, #7c2d12 75%, #dc2626 100%);
    position: relative;
    min-height: auto;
}

.gradient-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.1) 100%);
    z-index: 1;
}

.gradient-bg > * {
    position: relative;
    z-index: 2;
}

.gem-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    border: 1px solid var(--card-border);
    background: var(--card-bg);
    min-height: 420px;
    display: flex;
    flex-direction: column;
}

.gem-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px var(--shadow-medium);
    border-color: rgba(99, 102, 241, 0.3);
}

/* Line clamp utilities */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.search-input {
    backdrop-filter: blur(10px);
    background: var(--search-input-bg);
    border: 2px solid rgba(99, 102, 241, 0.1);
    font-weight: 500;
    color: var(--text-primary);
}

.search-input:focus {
    background: var(--bg-secondary);
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-input::placeholder {
    color: var(--text-tertiary);
}

.category-badge {
    transition: all 0.2s ease;
    font-weight: 600;
    border: 2px solid transparent;
}

.category-badge:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.category-badge.active {
    background: linear-gradient(135deg, #6366f1, #8b5cf6) !important;
    color: white !important;
    border-color: #6366f1;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.hero-pattern {
    background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.2) 1px, transparent 0);
    background-size: 24px 24px;
}

.text-shadow {
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.header-text-enhanced {
    color: #ffffff;
    text-shadow: 0 3px 15px rgba(0, 0, 0, 0.6), 0 1px 3px rgba(0, 0, 0, 0.8);
    font-weight: 800;
    background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.4));
}

.header-description {
    color: #ffffff;
    text-shadow: 0 2px 12px rgba(0, 0, 0, 0.7), 0 1px 3px rgba(0, 0, 0, 0.9);
    font-weight: 600;
    line-height: 1.5;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.04) 100%);
    backdrop-filter: blur(12px);
    border-radius: 16px;
    padding: 16px 24px;
    border: 1px solid rgba(255, 255, 255, 0.25);
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.25);
}

.feature-list li {
    font-weight: 500;
    color: var(--text-secondary);
}

.gem-description {
    color: var(--text-secondary);
    font-weight: 500;
    line-height: 1.6;
}

.stats-card {
    background: var(--stats-card-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--card-border);
}

.modal-content {
    max-height: 60vh;
    overflow-y: auto;
}

.modal-content::-webkit-scrollbar {
    width: 8px;
}

.modal-content::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
    border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb {
    background: var(--border-secondary);
    border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
    background: var(--text-tertiary);
}

.gem-content-preview {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    border: 2px dashed var(--border-secondary);
    border-radius: 12px;
    padding: 24px;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* Enhanced button styles */
.btn-primary {
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
    color: #1e40af;
    font-weight: 700;
    padding: 12px 24px;
    border-radius: 12px;
    border: 2px solid rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15), 0 3px 8px rgba(255, 255, 255, 0.1) inset;
    text-shadow: none;
    font-size: 14px;
    letter-spacing: 0.3px;
}

.btn-primary:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3), 0 6px 15px rgba(255, 255, 255, 0.2) inset;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-color: rgba(255, 255, 255, 1);
}

.btn-secondary {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    color: white;
    font-weight: 700;
    padding: 12px 24px;
    border-radius: 12px;
    border: 2px solid rgba(255, 255, 255, 0.6);
    transition: all 0.3s ease;
    backdrop-filter: blur(12px);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    font-size: 14px;
    letter-spacing: 0.3px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
    color: #1e40af;
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3), 0 6px 15px rgba(255, 255, 255, 0.2) inset;
    border-color: rgba(255, 255, 255, 1);
    text-shadow: none;
}

/* Animation classes */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Header section improvements */
.header-text-enhanced .lucide {
    filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.4));
}

/* Button container spacing and alignment */
.btn-primary, .btn-secondary {
    min-width: 180px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-primary .lucide, .btn-secondary .lucide {
    flex-shrink: 0;
}

/* Prose styling for markdown content */
.prose {
    color: var(--text-secondary);
    max-width: none;
}

.prose h1 {
    color: var(--text-primary);
    font-weight: 800;
    font-size: 2.25rem;
    margin-top: 0;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.prose h2 {
    color: var(--text-primary);
    font-weight: 700;
    font-size: 1.875rem;
    margin-top: 2rem;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.prose h3 {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1.5rem;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.prose p {
    margin-top: 1rem;
    margin-bottom: 1rem;
    line-height: 1.7;
}

.prose ul, .prose ol {
    margin-top: 1rem;
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.prose li {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

.prose strong {
    color: var(--text-primary);
    font-weight: 600;
}

.prose code {
    color: var(--code-text);
    background-color: var(--code-bg);
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.prose pre {
    background-color: var(--pre-bg);
    color: var(--pre-text);
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin-top: 1rem;
    margin-bottom: 1rem;
}

.prose pre code {
    background-color: transparent;
    color: inherit;
    padding: 0;
    border-radius: 0;
    font-size: 0.875rem;
}

.prose blockquote {
    border-left: 4px solid #6366f1;
    padding-left: 1rem;
    margin-left: 0;
    margin-right: 0;
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
    font-style: italic;
    color: var(--text-tertiary);
}

.prose hr {
    border: none;
    border-top: 1px solid var(--border-primary);
    margin-top: 2rem;
    margin-bottom: 2rem;
}

.prose a {
    color: #6366f1;
    text-decoration: underline;
    font-weight: 500;
}

.prose a:hover {
    color: #4f46e5;
}

/* Responsive improvements */
@media (max-width: 1024px) {
    .gem-card {
        min-height: 380px;
    }
}

@media (max-width: 768px) {
    .header-description {
        padding: 16px 24px;
        font-size: 16px;
    }

    .gem-card {
        padding: 20px;
        min-height: 360px;
    }

    .modal-content {
        max-height: 50vh;
    }

    .prose h1 {
        font-size: 1.875rem;
    }

    .prose h2 {
        font-size: 1.5rem;
    }

    .prose h3 {
        font-size: 1.25rem;
    }
}

@media (max-width: 640px) {
    .header-description {
        padding: 12px 20px;
        font-size: 15px;
    }

    .gem-card {
        min-height: 320px;
        padding: 16px;
    }

    .prose h1 {
        font-size: 1.5rem;
    }

    .prose h2 {
        font-size: 1.25rem;
    }
}

/* Theme toggle button styles */
.theme-toggle-btn {
    backdrop-filter: blur(16px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.theme-toggle-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* Dark mode specific adjustments */
.dark .gradient-bg {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%);
}

.dark .hero-pattern {
    background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.1) 1px, transparent 0);
}

/* Section backgrounds for dark mode */
.dark .bg-gradient-to-r.from-slate-50.to-blue-50 {
    background: linear-gradient(to right, var(--bg-secondary), var(--bg-tertiary)) !important;
}

.dark .bg-gradient-to-br.from-gray-50.to-indigo-50 {
    background: linear-gradient(to bottom right, var(--bg-primary), var(--bg-secondary)) !important;
}

.dark .bg-gradient-to-r.from-gray-900.to-gray-800 {
    background: linear-gradient(to right, #000000, #1a1a1a) !important;
}

/* Dark mode text color overrides */
.dark .prose a {
    color: #a5b4fc !important;
}

.dark .prose a:hover {
    color: #c7d2fe !important;
}

.dark .btn-primary {
    color: #ffffff !important;
    background: linear-gradient(135deg, #3730a3 0%, #1e40af 100%) !important;
}

.dark .btn-primary:hover {
    color: #ffffff !important;
    background: linear-gradient(135deg, #4338ca 0%, #2563eb 100%) !important;
}

.dark .btn-secondary:hover {
    color: #ffffff !important;
}

/* Ensure all text elements are white/near-white in dark mode */
.dark h1,
.dark h2,
.dark h3,
.dark h4,
.dark h5,
.dark h6 {
    color: #ffffff !important;
}

.dark p,
.dark span,
.dark div {
    color: var(--text-secondary) !important;
}

.dark .text-gray-600,
.dark .text-gray-500,
.dark .text-gray-400 {
    color: var(--text-secondary) !important;
}

.dark .text-gray-700,
.dark .text-gray-800,
.dark .text-gray-900 {
    color: var(--text-primary) !important;
}

/* Ensure gem card titles and descriptions are properly visible */
.dark .gem-card h3,
.dark .gem-card .font-bold,
.dark .text-gray-900 {
    color: #ffffff !important;
}

.dark .gem-card p,
.dark .gem-description {
    color: var(--text-secondary) !important;
}

/* Fix specific Tailwind text color classes in dark mode */
.dark .text-gray-700 {
    color: var(--text-secondary) !important;
}

.dark .text-gray-600 {
    color: var(--text-secondary) !important;
}

.dark .text-gray-500 {
    color: var(--text-tertiary) !important;
}

/* Fix category badge text visibility in dark mode */
.dark .category-badge {
    color: var(--text-primary) !important;
    background: var(--bg-secondary) !important;
    border-color: var(--border-primary) !important;
}

.dark .category-badge:hover {
    background: var(--bg-tertiary) !important;
    color: var(--text-primary) !important;
}

.dark .category-badge.active {
    background: linear-gradient(135deg, #6366f1, #8b5cf6) !important;
    color: white !important;
    border-color: #6366f1 !important;
}

@media (max-width: 480px) {
    .gem-card {
        min-height: 300px;
        padding: 14px;
    }

    .gem-card .flex.flex-col.sm\:flex-row {
        flex-direction: column;
        align-items: flex-start;
    }

    .gem-card .w-14.h-14 {
        width: 3rem;
        height: 3rem;
        margin-bottom: 0.75rem;
        margin-right: 0;
    }
}
