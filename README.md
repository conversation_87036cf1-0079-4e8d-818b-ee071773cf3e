# 💎 Gemini Gems

<div align="center">

![Gemini Gems](https://img.shields.io/badge/Gemini-Gems-blue?style=for-the-badge&logo=google&logoColor=white)
![Contributions Welcome](https://img.shields.io/badge/contributions-welcome-brightgreen?style=for-the-badge)
![License](https://img.shields.io/badge/license-MIT-green?style=for-the-badge)

**🚀 Supercharge your AI conversations with expertly crafted system prompts!**

*Transform Google's Gemini into specialized expert assistants with our professional-grade prompt collection*

[🌟 Explore Gems](#-available-gems) • [🤝 Contribute](#-contributing-guidelines) • [📖 Documentation](#-documentation) • [🚀 Quick Start](#-quick-start)

</div>

---

## 🎯 What are Gemini Gems?

**Gemini Gems** are meticulously crafted system prompts that transform general-purpose Gemini models into specialized expert assistants. Each gem is a blueprint for creating AI personas with:

- **🎭 Defined Personalities** - Clear roles and behavioral patterns
- **🧠 Specialized Knowledge** - Domain-specific expertise and capabilities
- **📋 Structured Workflows** - Consistent interaction protocols
- **🎨 Professional Output** - Formatted, actionable responses
- **🔄 Adaptive Behavior** - Context-aware responses and guidance

Think of gems as **professional consultants in AI form** - each one an expert in their field, ready to provide specialized assistance with consistent quality and reliability.

## 💎 Available Gems

Our collection features **13 specialized gems** across multiple categories, each designed for specific use cases:

### 📚 Education & Learning
- **[Grammar Teacher](grammar-teacher.md)** - Interactive Grade 3 grammar tutor with dynamic themed adventures
- **[Language Teacher](language-teacher.md)** - Personalized language learning with adaptive exercises and feedback

### 💻 Development & Technical
- **[Programming Co-pilot](programming-co-pilot.md)** - Expert coding assistant for all programming languages and frameworks
- **[System Architect](system-architect.md)** - Transforms ideas into comprehensive technical specifications

### 🎨 Creative & Content
- **[Storyteller/Novelist](storyteller-novelist.md)** - Master storyteller for compelling narratives and character development
- **[GameForge Master](gameforge-master.md)** - TTRPG and video game design specialist

### 💼 Business & Professional
- **[Marketing Strategist](marketing-strategist.md)** - Comprehensive marketing strategy development
- **[Professional Email Crafter](professional-email-crafter.md)** - Expert business communication specialist

### 🏠 Lifestyle & Practical
- **[DIY Tinkerer (Fix-It Felix)](diy-tinkerer.md)** - Home repair and improvement expert with safety-first approach
- **[Sous Chef (Chef Gusteau)](sous-chef.md)** - Culinary assistant for pantry-to-plate cooking

### 💰 Finance & Planning
- **[Personal Finance Navigator (CoinWise)](personal-finance-navigator.md)** - Financial education and investment strategy guidance

### 🧘 Wellness & Mindfulness
- **[Wellness & Mindfulness Coach (Serene)](wellness-mindfulness-coach.md)** - Personalized meditation and stress management

### 🙏 Spiritual & Cultural
- **[Dua Generator](dua-generator.md)** - Authentic Islamic prayers for various occasions

## 🚀 Quick Start

### Option 1: Browse Online (Recommended)
1. **Clone the Repository**:
   ```bash
   git clone https://github.com/nirzaf/gemini-gems.git
   cd gemini-gems
   ```

2. **Open the Website**: Simply open `index.html` in your web browser
   - **Double-click** `index.html` file, or
   - **Right-click** → "Open with" → Your preferred browser, or
   - **Drag and drop** `index.html` into your browser window

3. **Explore Gems**: Browse, preview, and copy gems with one click
4. **Use in Gemini**: Paste the copied prompt as your system message

### Option 2: Direct File Access
1. Browse the repository files directly
2. Open any `.md` file to view the gem
3. Copy the entire content
4. Use as system prompt in Google Gemini

## 📖 How to Use a Gem

**Step-by-step process:**

1. **Choose Your Gem** - Select a gem that matches your needs
2. **Copy the Content** - Copy the entire markdown content of the gem file
3. **Set as System Prompt** - In Google Gemini, paste the content as your system message
4. **Start Conversing** - The AI will now behave according to the gem's specifications

**Example:**
```
System Prompt: [Paste entire gem content here]
User Message: "Help me debug this Python function..."
```

The Gemini model will now respond as the specialized expert defined in the gem!

## 🤝 Contributing Guidelines

We welcome and encourage contributions! Here's everything you need to know about contributing to Gemini Gems:

### 🌟 Ways to Contribute

#### 1. **Create New Gems**
- Design specialized AI personas for specific use cases
- Follow our established patterns and quality standards
- Submit via pull request with proper documentation

#### 2. **Improve Existing Gems**
- Enhance clarity, functionality, or effectiveness
- Fix bugs or inconsistencies
- Add new features or capabilities

#### 3. **Enhance the Platform**
- Improve the web interface or user experience
- Add new features to the gem browser
- Optimize performance or accessibility

#### 4. **Documentation & Community**
- Improve documentation and examples
- Create tutorials or guides
- Help with translations or localization

### 📝 Creating a New Gem

#### **Step 1: Planning Your Gem**

**Define Your Concept:**
- **Target Audience**: Who will use this gem?
- **Core Purpose**: What specific problem does it solve?
- **Unique Value**: What makes it different from existing gems?
- **Scope**: What should it do (and not do)?

**Research & Validation:**
- Check existing gems to avoid duplication
- Research best practices in the domain
- Consider user needs and pain points
- Plan for different skill levels and use cases

#### **Step 2: Gem Structure & Content**

**Required Sections** (follow this exact structure):

```markdown
You are **[Persona Name]**, an expert AI [role description]. Your purpose is to [clear mission statement].

## Core Mission
[Detailed explanation of the gem's purpose and capabilities]

## Interaction Protocol
### 1. User Input
### 2. [Assessment/Analysis Phase]
### 3. [Response Generation]

## Required Output Structure
### [Section 1]
### [Section 2]
[etc.]

## Core Behavioral Directives
### [Directive Category 1]
### [Directive Category 2]
[etc.]

## Specialized Knowledge Areas
[Domain-specific expertise areas]

## Response Examples
[Example interactions and outputs]

## Important Limitations
[Boundaries, disclaimers, when to refer to professionals]

## Tone and Personality
[Communication style and approach]
```

#### **Step 3: Quality Standards**

**Content Requirements:**
- ✅ **Clear Persona**: Memorable name and distinct personality
- ✅ **Comprehensive Scope**: Covers the domain thoroughly
- ✅ **Structured Output**: Consistent, formatted responses
- ✅ **Safety Considerations**: Appropriate limitations and warnings
- ✅ **Professional Tone**: Expert but accessible communication
- ✅ **Practical Examples**: Real-world use cases and scenarios

**Technical Requirements:**
- ✅ **Markdown Format**: Proper formatting and structure
- ✅ **2000+ Words**: Comprehensive and detailed content
- ✅ **Consistent Style**: Follows established patterns
- ✅ **Error-Free**: Proper grammar, spelling, and formatting

### 🔧 Technical Contribution Process

#### **Step 4: Development Setup**

```bash
# 1. Fork the repository on GitHub
# 2. Clone your fork locally
git clone https://github.com/YOUR_USERNAME/gemini-gems.git
cd gemini-gems

# 3. Create a new branch for your gem
git checkout -b feature/your-gem-name

# 4. Open the website for testing
# Simply open index.html in your web browser
open index.html  # macOS
# or double-click index.html in file explorer
```

#### **Step 5: Adding Your Gem**

**Create the Gem File:**
```bash
# Create your gem file
touch your-gem-name.md

# Follow the naming convention:
# - Use lowercase with hyphens
# - Be descriptive but concise
# - Examples: diy-tinkerer.md, sous-chef.md
```

**Update the Application:**
1. **Add to JavaScript** (`assets/js/app.js`):
   ```javascript
   {
       id: 'your-gem-name',
       name: 'Your Gem Display Name',
       description: 'Brief description of what your gem does...',
       category: 'Appropriate Category',
       icon: 'lucide-icon-name',
       color: 'bg-color-class',
       file: 'your-gem-name.md',
       features: [
           'Key feature 1',
           'Key feature 2',
           'Key feature 3',
           'Key feature 4',
           'Key feature 5'
       ]
   }
   ```

2. **Update Categories** (if needed):
   - Add new category colors in `getCategoryColor()`
   - Add new category icons in `getCategoryIcon()`

#### **Step 6: Testing & Quality Assurance**

**Test Your Gem:**
- ✅ **Preview Functionality**: Ensure it displays correctly in the browser
- ✅ **Copy Feature**: Verify the copy button works
- ✅ **Content Quality**: Test with actual Gemini conversations
- ✅ **Mobile Responsive**: Check on different screen sizes
- ✅ **Cross-browser**: Test in Chrome, Firefox, Safari

**Quality Checklist:**
- ✅ **Markdown Validation**: Proper formatting and structure
- ✅ **Spelling & Grammar**: Use spell-check and proofreading
- ✅ **Link Verification**: All internal references work
- ✅ **Example Testing**: Verify examples produce expected results
- ✅ **Performance**: Gem loads quickly and efficiently

#### **Step 7: Submission Process**

**Prepare Your Pull Request:**
```bash
# 1. Commit your changes
git add .
git commit -m "feat: add [Gem Name] - [brief description]"

# 2. Push to your fork
git push origin feature/your-gem-name

# 3. Create Pull Request on GitHub
```

**Pull Request Requirements:**
- **Clear Title**: `feat: add [Gem Name] - [brief description]`
- **Detailed Description**: Explain the gem's purpose and capabilities
- **Testing Notes**: How you tested the gem
- **Screenshots**: Show the gem in the browser interface
- **Category Justification**: Why you chose the specific category

**PR Template:**
```markdown
## 💎 New Gem: [Gem Name]

### Description
Brief description of what this gem does and who it's for.

### Category
- **Category**: [Selected Category]
- **Justification**: Why this category fits

### Key Features
- Feature 1
- Feature 2
- Feature 3

### Testing
- [ ] Tested in Gemini conversations
- [ ] Verified browser preview functionality
- [ ] Checked mobile responsiveness
- [ ] Validated markdown formatting

### Screenshots
[Include screenshots of the gem in the browser]
```

### 🎯 Gem Categories & Guidelines

**Current Categories:**
- **📚 Education** - Learning, tutoring, skill development
- **💻 Development** - Programming, technical assistance
- **🎨 Creative** - Writing, design, artistic endeavors
- **💼 Business** - Professional, marketing, strategy
- **🏠 Lifestyle** - Home, cooking, practical daily tasks
- **💰 Finance** - Money management, investment education
- **🧘 Wellness** - Health, mindfulness, personal development
- **🙏 Spiritual** - Religious, cultural, philosophical guidance

**Category Selection Guidelines:**
- Choose the **primary use case** for your gem
- Consider the **target audience** and their needs
- If uncertain, propose a **new category** in your PR
- Ensure **balanced distribution** across categories

### 📋 Review Process

**What We Look For:**
1. **Quality & Completeness** - Comprehensive, well-structured content
2. **Originality** - Unique value proposition, not duplicating existing gems
3. **Usability** - Clear instructions, practical applications
4. **Safety** - Appropriate limitations and professional boundaries
5. **Community Value** - Benefits the broader user community

**Review Timeline:**
- **Initial Review**: 2-3 business days
- **Feedback Cycle**: Collaborative improvement process
- **Final Approval**: 1-2 business days after final changes
- **Merge & Deploy**: Immediate upon approval

**Common Feedback Areas:**
- Structure and formatting consistency
- Content depth and comprehensiveness
- Example quality and relevance
- Professional boundaries and limitations
- Category and feature accuracy

## 📖 Documentation

### 🏗️ Project Structure
```
gemini-gems/
├── README.md                    # This file
├── CODE_OF_CONDUCT.md          # Community guidelines
├── index.html                   # Main application interface
├── assets/
│   ├── css/styles.css          # Styling and themes
│   └── js/app.js               # Application logic
└── *.md                        # Individual gem files
```

### 🎨 Design System

**Color Palette:**
- **Primary**: Blue gradient (#1e40af → #3730a3)
- **Categories**: Distinct colors for each category
- **Accent**: Purple, teal, orange variations
- **Text**: High contrast for accessibility

**Typography:**
- **Headers**: Bold, clear hierarchy
- **Body**: Readable, professional fonts
- **Code**: Monospace for technical content

**Components:**
- **Gem Cards**: Consistent layout and information
- **Modal Previews**: Full-screen content viewing
- **Buttons**: Clear call-to-action styling
- **Categories**: Color-coded organization

### 🔧 Technical Details

**Frontend Technologies:**
- **HTML5** - Semantic structure
- **CSS3** - Modern styling with gradients and animations
- **JavaScript (ES6+)** - Interactive functionality
- **Alpine.js** - Reactive data binding
- **Marked.js** - Markdown parsing and rendering
- **Lucide Icons** - Consistent iconography

**Deployment:**
- **Static Website** - No server required, runs directly in browser
- **File-based** - All gems stored as individual markdown files
- **Client-side Rendering** - Dynamic content loaded via JavaScript

**Browser Support:**
- **Modern Browsers** - Chrome, Firefox, Safari, Edge
- **Mobile Responsive** - Optimized for all screen sizes
- **Progressive Enhancement** - Graceful degradation

## 🌟 Community & Support

### 💬 Getting Help

**Questions & Discussions:**
- **GitHub Issues** - Bug reports and feature requests
- **GitHub Discussions** - Community questions and ideas
- **Documentation** - Comprehensive guides and examples

**Response Times:**
- **Bug Reports** - 24-48 hours
- **Feature Requests** - 3-5 business days
- **General Questions** - 1-2 business days

### 🏆 Recognition

**Contributors** are recognized through:
- **GitHub Contributors** section
- **Gem Attribution** - Creator credit in gem metadata
- **Community Highlights** - Featured contributions
- **Maintainer Opportunities** - Active contributors invited to help maintain

### 📜 Code of Conduct

We are committed to providing a welcoming and inclusive environment for all contributors. Please read and follow our [Code of Conduct](CODE_OF_CONDUCT.md).

**Core Principles:**
- **Respect** - Treat all community members with dignity
- **Inclusivity** - Welcome diverse perspectives and backgrounds
- **Collaboration** - Work together constructively
- **Quality** - Maintain high standards for all contributions

## 📄 License & Legal

**License:** MIT License - see [LICENSE](LICENSE) file for details

**Contributions:** By contributing, you agree that your contributions will be licensed under the MIT License

**Attribution:** Contributors retain credit for their specific contributions

**Usage:** Gems can be used freely for personal and commercial purposes

---

<div align="center">

**🚀 Ready to contribute? Start by exploring our existing gems, then create your own!**

[🌟 Browse Gems](#-available-gems) • [🤝 Start Contributing](#-contributing-guidelines) • [📖 Read Docs](#-documentation)

**Made with ❤️ by the Gemini Gems community**

</div>
