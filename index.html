<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini Gems - AI Persona Collection</title>
    <meta name="description" content="A curated collection of specialized AI personas for Google's Gemini models. Transform your AI into expert assistants for various tasks.">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Marked.js for Markdown parsing -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>

    <!-- Custom Styles -->
    <link rel="stylesheet" href="assets/css/styles.css">
</head>
<body class="transition-colors duration-300" x-data="geminiGems()">
    <!-- Header -->
    <header class="gradient-bg hero-pattern">
        <div class="container mx-auto px-6 py-6">
            <!-- Theme Toggle Button -->
            <div class="flex justify-end mb-4">
                <button @click="toggleTheme()" 
                        class="theme-toggle-btn p-3 rounded-xl backdrop-filter backdrop-blur-lg bg-white bg-opacity-20 border border-white border-opacity-30 text-white hover:bg-opacity-30 transition-all duration-300 group"
                        :title="getThemeLabel()">
                    <i :class="getThemeIcon()" class="text-lg group-hover:scale-110 transition-transform duration-200"></i>
                </button>
            </div>
            
            <div class="text-center">
                <h1 class="text-3xl md:text-4xl font-bold mb-3 header-text-enhanced fade-in">
                    <i data-lucide="gem" class="inline-block w-8 h-8 mr-2"></i>
                    Gemini Gems
                </h1>
                <div class="max-w-3xl mx-auto mb-4 slide-up">
                    <p class="header-description text-sm md:text-base leading-relaxed">
                        🚀 <strong>Supercharge your AI conversations</strong> with expertly crafted system prompts!<br>
                        ✨ Transform Google's Gemini into <strong>specialized expert assistants</strong><br>
                        💎 <strong>Professional-grade prompts</strong> ready to copy, paste, and deploy instantly
                    </p>
                </div>
                <div class="flex flex-col sm:flex-row gap-4 justify-center fade-in">
                    <a href="#gems" class="btn-primary">
                        <i data-lucide="sparkles" class="inline-block w-4 h-4 mr-2"></i>
                        Explore Gems
                    </a>
                    <a href="https://github.com/nirzaf/gemini-gems" target="_blank" class="btn-secondary">
                        <i data-lucide="github" class="inline-block w-4 h-4 mr-2"></i>
                        View on GitHub
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Stats Section -->
    <section class="py-10 bg-gradient-to-r from-slate-50 to-blue-50 transition-colors duration-300">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                <div class="stats-card p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow">
                    <div class="text-4xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-2" x-text="gems.length"></div>
                    <div class="text-gray-700 font-semibold text-base">Available Gems</div>
                    <div class="text-gray-500 text-xs mt-1">Ready to use AI personas</div>
                </div>
                <div class="stats-card p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow">
                    <div class="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2" x-text="categories.length"></div>
                    <div class="text-gray-700 font-semibold text-base">Categories</div>
                    <div class="text-gray-500 text-xs mt-1">Specialized domains</div>
                </div>
                <div class="stats-card p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow">
                    <div class="text-4xl font-bold bg-gradient-to-r from-pink-600 to-red-600 bg-clip-text text-transparent mb-2">100%</div>
                    <div class="text-gray-700 font-semibold text-base">Open Source</div>
                    <div class="text-gray-500 text-xs mt-1">Free for everyone</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Search and Filter Section -->
    <section id="gems" class="py-12 bg-gradient-to-br from-gray-50 to-indigo-50 transition-colors duration-300">
        <div class="container mx-auto px-6">
            <div class="text-center mb-8">
                <h2 class="text-3xl md:text-4xl font-bold mb-3" style="color: var(--text-primary);">Discover Your Perfect AI Persona</h2>
                <p class="text-lg max-w-2xl mx-auto" style="color: var(--text-secondary);">Browse our collection of specialized gems and find the perfect AI assistant for your needs.</p>
            </div>

            <div class="max-w-6xl mx-auto mb-8">
                <!-- Search Bar -->
                <div class="relative mb-6">
                    <input
                        type="text"
                        x-model="searchQuery"
                        placeholder="Search gems by name, description, features, or use case..."
                        class="search-input w-full px-4 sm:px-6 py-3 sm:py-4 pl-10 sm:pl-12 rounded-xl text-sm sm:text-base placeholder-gray-500 shadow-md"
                    >
                    <i data-lucide="search" class="absolute left-3 sm:left-4 top-1/2 transform -translate-y-1/2 text-indigo-400 w-4 h-4 sm:w-5 sm:h-5"></i>
                </div>

                <!-- Category Filters -->
                <div class="flex flex-wrap gap-2 sm:gap-3 justify-center">
                    <button
                        @click="selectedCategory = 'all'"
                        :class="selectedCategory === 'all' ? 'active' : 'bg-white text-gray-700 hover:bg-indigo-50 hover:text-indigo-700'"
                        class="category-badge px-4 sm:px-6 py-2 rounded-full shadow-sm text-xs sm:text-sm"
                    >
                        <i data-lucide="sparkles" class="inline-block w-3 h-3 mr-1"></i>
                        <span class="hidden sm:inline">All Gems</span>
                        <span class="sm:hidden">All</span>
                    </button>
                    <template x-for="category in categories" :key="category">
                        <button
                            @click="selectedCategory = category"
                            :class="selectedCategory === category ? 'active' : 'bg-white text-gray-700 hover:bg-indigo-50 hover:text-indigo-700'"
                            class="category-badge px-4 sm:px-6 py-2 rounded-full shadow-sm text-xs sm:text-sm"
                        >
                            <i :data-lucide="getCategoryIcon(category)" class="inline-block w-3 h-3 mr-1"></i>
                            <span x-text="category"></span>
                        </button>
                    </template>
                </div>
            </div>

            <!-- Results Count -->
            <div class="text-center mb-6">
                <p class="text-base font-medium" style="color: var(--text-secondary);">
                    Showing <span class="font-bold text-indigo-600" x-text="filteredGems.length"></span>
                    of <span class="font-bold text-indigo-600" x-text="gems.length"></span> gems
                </p>
            </div>

            <!-- Gems Grid -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8">
                <template x-for="gem in filteredGems" :key="gem.id">
                    <div class="gem-card rounded-2xl p-6 shadow-xl flex flex-col h-full">
                        <!-- Gem Header -->
                        <div class="flex flex-col sm:flex-row items-start sm:items-center mb-4">
                            <div class="w-14 h-14 rounded-xl flex items-center justify-center mb-3 sm:mb-0 sm:mr-4 shadow-lg flex-shrink-0"
                                 :class="gem.color">
                                <i :class="getFontAwesomeIcon(gem.category)" class="text-xl text-white"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <h3 class="text-lg sm:text-xl font-bold text-gray-900 mb-2 line-clamp-2" x-text="gem.name"></h3>
                                <span class="inline-block px-3 py-1 text-xs font-bold rounded-full"
                                      :class="getCategoryColor(gem.category)"
                                      x-text="gem.category">
                                </span>
                            </div>
                        </div>

                        <!-- Gem Description -->
                        <p class="gem-description mb-4 text-sm leading-relaxed flex-grow line-clamp-3" x-text="gem.description"></p>

                        <!-- Gem Features -->
                        <div class="mb-6 flex-grow">
                            <h4 class="text-base font-bold text-gray-900 mb-3">Key Features:</h4>
                            <ul class="feature-list space-y-2">
                                <template x-for="feature in gem.features.slice(0, 3)" :key="feature">
                                    <li class="text-xs flex items-start">
                                        <i data-lucide="check-circle" class="w-4 h-4 text-emerald-500 mr-2 flex-shrink-0 mt-0.5"></i>
                                        <span class="line-clamp-2" x-text="feature"></span>
                                    </li>
                                </template>
                            </ul>
                        </div>

                        <!-- Gem Actions -->
                        <div class="flex flex-col sm:flex-row gap-2 mt-auto">
                            <button @click="previewGem(gem)"
                                    class="flex-1 bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-3 py-2.5 rounded-lg text-center text-sm font-bold hover:from-blue-700 hover:to-indigo-700 transition-all transform hover:scale-105 shadow-lg">
                                <i data-lucide="eye" class="inline-block w-4 h-4 mr-1"></i>
                                <span class="hidden sm:inline">Preview</span>
                                <span class="sm:hidden">View</span>
                            </button>
                            <a :href="gem.file"
                               target="_blank"
                               class="flex-1 bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-3 py-2.5 rounded-lg text-center text-sm font-bold hover:from-indigo-700 hover:to-purple-700 transition-all transform hover:scale-105 shadow-lg">
                                <i data-lucide="download" class="inline-block w-4 h-4 mr-1"></i>
                                <span class="hidden sm:inline">Get Gem</span>
                                <span class="sm:hidden">Get</span>
                            </a>
                        </div>
                    </div>
                </template>
            </div>

            <!-- No Results -->
            <div x-show="filteredGems.length === 0" class="text-center py-16">
                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i data-lucide="search-x" class="w-12 h-12 text-gray-400"></i>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 mb-3">No gems found</h3>
                <p class="text-lg text-gray-600 mb-6">Try adjusting your search or filter criteria</p>
                <button @click="searchQuery = ''; selectedCategory = 'all'"
                        class="bg-indigo-600 text-white px-6 py-3 rounded-xl font-semibold hover:bg-indigo-700 transition-colors">
                    Clear Filters
                </button>
            </div>
        </div>
    </section>


    <!-- Footer -->
    <footer class="bg-gradient-to-r from-gray-900 to-gray-800 text-white py-16">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-12">
                <div>
                    <h3 class="text-2xl font-bold mb-6">
                        <i data-lucide="gem" class="inline-block w-8 h-8 mr-3"></i>
                        Gemini Gems
                    </h3>
                    <p class="text-gray-300 mb-6 text-lg leading-relaxed">
                        A community-driven collection of specialized AI personas for Google's Gemini models.
                    </p>
                    <div class="flex space-x-6">
                        <a href="https://github.com/nirzaf/gemini-gems" target="_blank" class="text-gray-300 hover:text-white transition-colors transform hover:scale-110">
                            <i data-lucide="github" class="w-8 h-8"></i>
                        </a>
                    </div>
                </div>
                <div>
                    <h4 class="text-xl font-bold mb-6">Quick Links</h4>
                    <ul class="space-y-4">
                        <li><a href="#gems" class="text-gray-300 hover:text-white transition-colors text-lg hover:underline">Browse Gems</a></li>
                        <li><a href="https://github.com/nirzaf/gemini-gems/blob/main/README.md" target="_blank" class="text-gray-300 hover:text-white transition-colors text-lg hover:underline">Documentation</a></li>
                        <li><a href="https://github.com/nirzaf/gemini-gems/issues" target="_blank" class="text-gray-300 hover:text-white transition-colors text-lg hover:underline">Report Issues</a></li>
                        <li><a href="https://github.com/nirzaf/gemini-gems/blob/main/CODE_OF_CONDUCT.md" target="_blank" class="text-gray-300 hover:text-white transition-colors text-lg hover:underline">Code of Conduct</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-xl font-bold mb-6">Contribute</h4>
                    <p class="text-gray-300 mb-6 text-lg leading-relaxed">Help us grow this collection by contributing your own gems or improving existing ones.</p>
                    <a href="https://github.com/nirzaf/gemini-gems/fork" target="_blank" class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-3 rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all transform hover:scale-105 inline-block font-bold shadow-lg">
                        <i data-lucide="git-fork" class="inline-block w-5 h-5 mr-2"></i>
                        Fork & Contribute
                    </a>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-12 pt-8 text-center">
                <p class="text-gray-300 text-lg">
                    &copy; 2025 Gemini Gems. Open source project under MIT License.
                </p>
            </div>
        </div>
    </footer>

    <!-- Toast Notification -->
    <div x-show="showToast"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform translate-y-2 scale-95"
         x-transition:enter-end="opacity-100 transform translate-y-0 scale-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform translate-y-0 scale-100"
         x-transition:leave-end="opacity-0 transform translate-y-2 scale-95"
         class="fixed bottom-6 right-6 bg-gradient-to-r from-emerald-500 to-green-500 text-white px-8 py-4 rounded-2xl shadow-2xl z-50 backdrop-blur-sm">
        <div class="flex items-center">
            <i data-lucide="check-circle" class="w-6 h-6 mr-3"></i>
            <span class="font-semibold text-lg" x-text="toastMessage"></span>
        </div>
    </div>

    <!-- Preview Modal -->
    <div x-show="showPreview"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
         @click.self="showPreview = false">
        <div x-show="showPreview"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform scale-95"
             x-transition:enter-end="opacity-100 transform scale-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100 transform scale-100"
             x-transition:leave-end="opacity-0 transform scale-95"
             class="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">

            <!-- Modal Header -->
            <div class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-xl flex items-center justify-center mr-4 bg-white bg-opacity-20">
                            <i :data-lucide="selectedGem?.icon" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold" x-text="selectedGem?.name"></h3>
                            <p class="text-indigo-100" x-text="selectedGem?.category"></p>
                        </div>
                    </div>
                    <button @click="showPreview = false" class="text-white hover:text-gray-200 transition-colors">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>
            </div>

            <!-- Modal Content -->
            <div class="modal-content p-6">
                <div x-show="gemContent && !loadingContent">
                    <div class="prose prose-lg max-w-none bg-white rounded-xl p-6 border shadow-sm" x-html="parsedContent"></div>
                </div>
                <div x-show="loadingContent" class="text-center py-12">
                    <div class="animate-spin w-12 h-12 border-4 border-indigo-600 border-t-transparent rounded-full mx-auto mb-6"></div>
                    <p class="text-gray-600 text-lg">Loading gem content...</p>
                </div>
                <div x-show="!gemContent && !loadingContent && errorMessage" class="text-center py-12">
                    <div class="text-amber-500 mb-4">
                        <i data-lucide="server" class="w-12 h-12 mx-auto mb-4"></i>
                    </div>
                    <p class="text-amber-600 text-lg font-semibold mb-2">Local Server Required</p>
                    <p class="text-gray-600 mb-4" x-text="errorMessage"></p>
                    <div class="flex gap-3 justify-center">
                        <button @click="previewGem(selectedGem)" class="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                            Try Again
                        </button>
                        <a href="SERVER_SETUP.md" target="_blank" class="bg-amber-600 text-white px-6 py-2 rounded-lg hover:bg-amber-700 transition-colors">
                            Setup Guide
                        </a>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="bg-gray-50 px-6 py-4 flex gap-3">
                <button @click="copyGemContent()"
                        class="flex-1 bg-gradient-to-r from-emerald-600 to-green-600 text-white px-6 py-3 rounded-xl font-bold hover:from-emerald-700 hover:to-green-700 transition-all">
                    <i data-lucide="copy" class="inline-block w-4 h-4 mr-2"></i>
                    Copy Content
                </button>
                <a :href="selectedGem?.file"
                   target="_blank"
                   class="flex-1 bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-6 py-3 rounded-xl text-center font-bold hover:from-indigo-700 hover:to-purple-700 transition-all">
                    <i data-lucide="download" class="inline-block w-4 h-4 mr-2"></i>
                    Download File
                </a>
                <button @click="showPreview = false"
                        class="px-6 py-3 border-2 border-gray-300 rounded-xl hover:bg-gray-100 transition-all font-bold text-gray-700">
                    Close
                </button>
            </div>
        </div>
    </div>

    <!-- Custom JavaScript -->
    <script src="assets/js/app.js"></script>

</body>
</html>
